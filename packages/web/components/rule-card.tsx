"use client";

import { useState } from "react";
import { use<PERSON><PERSON> } from "jotai";
import {
  Card,
  Badge,
  Button,
  DropdownMenu,
  Dialog,
  Tooltip,
  Flex,
  Box
} from "@radix-ui/themes";
import {
  MoreHorizontal,
  Copy,
  Share,
  Download,
  Edit,
  Trash,
  Eye,
  Code,
  FileJson,
  FileText,
  Terminal,
  ExternalLink,

  Star

} from "lucide-react";
import { Rule, idePreferencesAtom, APPLY_TYPE_METADATA } from "@/lib/store";
import {
  generateIDECommand,
  generateBasicCommand,
  sortIDEPreferences,
  copyToClipboard
} from "@/lib/ide-utils";
import {
  generateMDCFromRule
} from "@/lib/mdc-utils";
import { CodeEditor } from "@/components/ui/code-editor";
import { toast } from "sonner";
import Link from "next/link";


/**
 * Copy Rule as MDC formatted text to clipboard
 */
export async function copyRuleToClipboard(rule: Rule): Promise<boolean> {
  try {
    const mdcText = generateMDCFromRule(rule);
    await navigator.clipboard.writeText(mdcText);
    return true;
  } catch (error) {
    console.error('Failed to copy to clipboard:', error);
    return false;
  }
}
interface RuleCardProps {
  rule: Rule;
  onEdit?: (rule: Rule) => void;
  onDelete?: (ruleId: string) => void;
  isOwner?: boolean;
}

export function RuleCard({ rule, onEdit, onDelete, isOwner = false }: RuleCardProps) {
  const [showContent, setShowContent] = useState(false);
  const [copiedCommand, setCopiedCommand] = useState(false);
  const [idePreferences] = useAtom(idePreferencesAtom);
  
  const handleCopy = async () => {
    await navigator.clipboard.writeText(rule.content);
    toast.success("Rule content copied to clipboard");
  };

  const handleCopyCommand = async () => {
    // Only available for public rules
    if (rule.visibility !== "PUBLIC") {
      toast.error("Only public rules can be used with npx command");
      return;
    }

    const rawDataUrl = `${window.location.origin}/api/rules/raw?id=${rule.id}`;
    const command = generateBasicCommand(rawDataUrl);

    const success = await copyToClipboard(command);
    if (success) {
      setCopiedCommand(true);
      toast.success("CLI command copied to clipboard");

      // Reset the copied state after 2 seconds
      setTimeout(() => setCopiedCommand(false), 2000);
    } else {
      toast.error("Failed to copy command to clipboard");
    }
  };

  const handleCopyIDECommand = async (ideId: string) => {
    // Only available for public rules
    if (rule.visibility !== "PUBLIC") {
      toast.error("Only public rules can be used with npx command");
      return;
    }

    const ide = idePreferences.preferredIDEs.find(ide => ide.id === ideId);
    if (!ide) return;

    const rawDataUrl = `${window.location.origin}/api/rules/raw?id=${rule.id}`;
    const command = generateIDECommand(rawDataUrl, ide.type);

    const success = await copyToClipboard(command);
    if (success) {
      toast.success(`${ide.name} command copied to clipboard`);
    } else {
      toast.error("Failed to copy command to clipboard");
    }
  };

  const handleCopyDefaultIDECommand = async () => {
    // Only available for public rules
    if (rule.visibility !== "PUBLIC") {
      toast.error("Only public rules can be used with npx command");
      return;
    }

    const defaultIDE = idePreferences.preferredIDEs.find(ide => ide.id === idePreferences.defaultIDE);
    if (!defaultIDE) {
      // Fallback to basic command if no default IDE
      return handleCopyCommand();
    }

    const rawDataUrl = `${window.location.origin}/api/rules/raw?id=${rule.id}`;
    const command = generateIDECommand(rawDataUrl, defaultIDE.type);

    const success = await copyToClipboard(command);
    if (success) {
      setCopiedCommand(true);
      toast.success(`${defaultIDE.name} command copied to clipboard`);

      // Reset the copied state after 2 seconds
      setTimeout(() => setCopiedCommand(false), 2000);
    } else {
      toast.error("Failed to copy command to clipboard");
    }
  };

  const handleShare = async () => {
    if (rule.visibility === "PUBLIC") {
      const shareUrl = `${window.location.origin}/rules/${rule.id}`;
      await navigator.clipboard.writeText(shareUrl);
      toast.success("Share link copied to clipboard");
    } else if (rule.shareToken) {
      const shareUrl = `${window.location.origin}/shared/${rule.shareToken}`;
      await navigator.clipboard.writeText(shareUrl);
      toast.success("Share link copied to clipboard");
    }
  };

  const handleDownloadJSON = () => {
    const data = {
      title: rule.title,
      description: rule.description,
      content: rule.content,
      ideType: rule.ideType,
      tags: rule.tags.map(t => t.tag.name),
    };
    
    const blob = new Blob([JSON.stringify(data, null, 2)], {
      type: "application/json",
    });
    
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `${rule.title.toLowerCase().replace(/\s+/g, "-")}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    toast.success("Rule downloaded as JSON");
  };

  const handleDownloadMDX = async () => {
    // Only download MDX for public rules via API
    if (rule.visibility !== "PUBLIC") {
      toast.error("Only public rules can be downloaded as MDX");
      return;
    }

    try {
      const response = await fetch(`/api/rules/download?id=${rule.id}`);
      if (!response.ok) {
        throw new Error("Failed to download rule");
      }
      
      const blob = await response.blob();
      const url = URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = `${rule.title.toLowerCase().replace(/\s+/g, "-")}.mdx`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      
      toast.success("Rule downloaded as MDX");
    } catch (error) {
      toast.error("Failed to download rule as MDX");
    }
  };

  const getIDEBadgeColor = (ideType: string) => {
    switch (ideType) {
      case "CURSOR":
        return "bg-blue-500";
      case "AUGMENT":
        return "bg-green-500";
      case "WINDSURF":
        return "bg-purple-500";
      case "CLAUDE":
        return "bg-orange-500";
      case "GITHUB_COPILOT":
        return "bg-gray-800";
      case "GEMINI":
        return "bg-indigo-500";
      case "OPENAI_CODEX":
        return "bg-teal-500";
      case "CLINE":
        return "bg-pink-500";
      case "JUNIE":
        return "bg-yellow-500";
      case "TRAE":
        return "bg-red-500";
      case "LINGMA":
        return "bg-cyan-500";
      case "KIRO":
        return "bg-emerald-500";
      case "TENCENT_CODEBUDDY":
        return "bg-violet-500";
      default:
        return "bg-gray-500";
    }
  };

  return (
    <>
      <Card className="group hover:shadow-md transition-all duration-200 mobile-card">
        <div className="pb-3">
          <Flex align="start" justify="between" gap="3">
            <Box className="space-y-2" style={{ flex: 1, minWidth: 0 }}>
              {rule.visibility === "PUBLIC" ? (
                <Link href={`/rules/${rule.id}`} className="touch-target">
                  <Flex
                    align="center"
                    gap="1"
                    display="inline-flex"
                    className="text-base xs:text-lg font-semibold cursor-pointer hover:text-primary transition-colors"
                  >
                    <span className="truncate">{rule.title}</span>
                    <ExternalLink className="h-3 w-3 opacity-0 group-hover:opacity-50" style={{ flexShrink: 0 }} />
                  </Flex>
                </Link>
              ) : (
                <div
                  className="text-base xs:text-lg font-semibold cursor-pointer hover:text-primary transition-colors touch-target truncate"
                  onClick={() => setShowContent(true)}
                >
                  {rule.title}
                </div>
              )}
              {rule.description && (
                <p className="text-xs xs:text-sm text-muted-foreground line-clamp-2 leading-relaxed">
                  {rule.description}
                </p>
              )}
            </Box>
            <DropdownMenu.Root>
              <DropdownMenu.Trigger>
                <Button
                  variant="ghost"
                  size="1"
                  className="opacity-60 xs:opacity-0 xs:group-hover:opacity-100 transition-opacity touch-target"
                  style={{ flexShrink: 0 }}
                >
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenu.Trigger>
              <DropdownMenu.Content align="end">
                <DropdownMenu.Item onClick={() => setShowContent(true)}>
                  <Eye className="mr-2 h-4 w-4" />
                  View
                </DropdownMenu.Item>
                {rule.visibility === "PUBLIC" && (
                  <DropdownMenu.Item asChild>
                    <Link href={`/rules/${rule.id}`}>
                      <ExternalLink className="mr-2 h-4 w-4" />
                      Open Rule Page
                    </Link>
                  </DropdownMenu.Item>
                )}
                <DropdownMenu.Item onClick={handleCopy}>
                  <Copy className="mr-2 h-4 w-4" />
                  Copy Content
                </DropdownMenu.Item>
                <DropdownMenu.Item onClick={async () => {
                  const success = await copyRuleToClipboard(rule);
                  if (success) {
                    toast.success("Rule copied as MDC format to clipboard");
                  } else {
                    toast.error("Failed to copy rule to clipboard");
                  }
                }}>
                  <FileText className="mr-2 h-4 w-4" />
                  Copy as MDC
                </DropdownMenu.Item>
                  <>
                    {/* CLI Commands Submenu */}
                    <DropdownMenu.Sub>
                      <DropdownMenu.SubTrigger>
                        <Terminal className="mr-2 h-4 w-4" />
                        Copy CLI Command
                      </DropdownMenu.SubTrigger>
                      <DropdownMenu.SubContent>
                        {/* Default IDE Command (if available) */}
                        {idePreferences.defaultIDE && idePreferences.preferredIDEs.length > 0 && (
                          <>
                            <DropdownMenu.Item onClick={handleCopyDefaultIDECommand}>
                              <Star className="mr-2 h-4 w-4 fill-current" />
                              {idePreferences.preferredIDEs.find(ide => ide.id === idePreferences.defaultIDE)?.name} (Default)
                            </DropdownMenu.Item>
                            <DropdownMenu.Separator />
                          </>
                        )}

                        {/* Basic Command */}
                        <DropdownMenu.Item onClick={handleCopyCommand}>
                          <Terminal className="mr-2 h-4 w-4" />
                          Basic Command
                        </DropdownMenu.Item>

                        {/* Preferred IDEs Commands */}
                        {idePreferences.preferredIDEs.length > 0 && (
                          <>
                            <DropdownMenu.Separator />
                            {sortIDEPreferences(idePreferences.preferredIDEs, idePreferences.defaultIDE)
                              .filter(ide => ide.id !== idePreferences.defaultIDE) // Don't duplicate default
                              .map((ide) => (
                                <DropdownMenu.Item
                                  key={ide.id}
                                  onClick={() => handleCopyIDECommand(ide.id)}
                                >
                                  <Code className="mr-2 h-4 w-4" />
                                  {ide.name}
                                </DropdownMenu.Item>
                              ))}
                          </>
                        )}

                        {/* No preferences message */}
                        {idePreferences.preferredIDEs.length === 0 && (
                          <div className="px-2 py-1.5 text-xs text-muted-foreground">
                            Add IDE preferences in settings for quick commands
                          </div>
                        )}
                      </DropdownMenu.SubContent>
                    </DropdownMenu.Sub>

                    <DropdownMenu.Item onClick={handleShare}>
                      <Share className="mr-2 h-4 w-4" />
                      Share
                    </DropdownMenu.Item>
                  </>
                <DropdownMenu.Sub>
                  <DropdownMenu.SubTrigger>
                    <Download className="mr-2 h-4 w-4" />
                    Download
                  </DropdownMenu.SubTrigger>
                  <DropdownMenu.SubContent>
                    <DropdownMenu.Item onClick={handleDownloadJSON}>
                      <FileJson className="mr-2 h-4 w-4" />
                      JSON
                    </DropdownMenu.Item>
                      <DropdownMenu.Item onClick={handleDownloadMDX}>
                        <FileText className="mr-2 h-4 w-4" />
                        MDX
                      </DropdownMenu.Item>
                  </DropdownMenu.SubContent>
                </DropdownMenu.Sub>
                {isOwner && onEdit && (
                  <>
                    <DropdownMenu.Separator />
                    <DropdownMenu.Item onClick={() => onEdit(rule)}>
                      <Edit className="mr-2 h-4 w-4" />
                      Edit
                    </DropdownMenu.Item>
                  </>
                )}
                {isOwner && onDelete && (
                  <DropdownMenu.Item
                    onClick={() => onDelete(rule.id)}
                    className="text-destructive"
                  >
                    <Trash className="mr-2 h-4 w-4" />
                    Delete
                  </DropdownMenu.Item>
                )}
              </DropdownMenu.Content>
            </DropdownMenu.Root>
          </Flex>
        </div>

        <div className="space-y-3 xs:space-y-4">
          <Flex align="center" gap="2" wrap="wrap">
            <Badge
              variant="soft"
              className={`${getIDEBadgeColor(rule.ideType)} text-white text-xs xs:text-sm`}
            >
              <Code className="mr-1 h-3 w-3" />
              {rule.ideType}
            </Badge>
            <Badge
              variant="soft"
              color={APPLY_TYPE_METADATA[rule.applyType]?.color as any || 'gray'}
              className="text-xs xs:text-sm"
            >
              {APPLY_TYPE_METADATA[rule.applyType]?.name || rule.applyType}
            </Badge>
            {rule.visibility === "PUBLIC" && (
              <Badge variant="outline" className="text-xs xs:text-sm">Public</Badge>
            )}
          </Flex>

          {rule.tags.length > 0 && (
            <Flex wrap="wrap" gap={{ initial: '1', xs: '2' }}>
              {rule.tags.slice(0, 3).map((ruleTag) => (
                <Badge
                  key={ruleTag.tag.id}
                  variant="outline"
                  style={{ borderColor: ruleTag.tag.color }}
                  className="text-xs px-2 py-1"
                >
                  {ruleTag.tag.name}
                </Badge>
              ))}
              {rule.tags.length > 3 && (
                <Badge variant="outline" className="text-xs px-2 py-1 text-muted-foreground">
                  +{rule.tags.length - 3} more
                </Badge>
              )}
            </Flex>
          )}
          
          <div className="text-xs text-muted-foreground">
            Updated {new Date(rule.updatedAt).toLocaleDateString()}
          </div>
        </div>
      </Card>

      <Dialog.Root open={showContent} onOpenChange={setShowContent}>
        <Dialog.Content className="max-w-4xl max-h-[90vh] w-[95vw] xs:w-[90vw] overflow-hidden">
          <div className="space-y-2">
            <Dialog.Title className="text-lg xs:text-xl truncate">{rule.title}</Dialog.Title>
            {rule.description && (
              <Dialog.Description className="text-sm xs:text-base">
                {rule.description}
              </Dialog.Description>
            )}
          </div>
          <div className="flex-1 overflow-hidden">
            <CodeEditor 
              value={rule.content} 
              onChange={() => {}} 
              className="h-[50vh] xs:h-[60vh]"
            />
          </div>
          {rule.visibility === "PUBLIC" && (
            <div className="mt-4 space-y-3">
              {/* Default IDE Command (if available) */}
              {idePreferences.defaultIDE && idePreferences.preferredIDEs.length > 0 && (
                <div className="p-3 bg-primary/5 border border-primary/20 rounded-lg">
                  <div className="flex items-center justify-between">
                    <div className="space-y-1">
                      <p className="text-sm font-medium flex items-center gap-2">
                        <Star className="h-4 w-4 fill-current text-primary" />
                        {idePreferences.preferredIDEs.find(ide => ide.id === idePreferences.defaultIDE)?.name} (Default):
                      </p>
                      <code className="text-xs bg-background px-2 py-1 rounded block">
                        {generateIDECommand(`${window.location.origin}/api/rules/raw?id=${rule.id}`,
                          idePreferences.preferredIDEs.find(ide => ide.id === idePreferences.defaultIDE)?.type || 'GENERAL')}
                      </code>
                    </div>
                    <Button
                      size="1"
                      variant="outline"
                      onClick={handleCopyDefaultIDECommand}
                      className="ml-2"
                    >
                      <Terminal className="h-4 w-4 mr-2" />
                      Copy
                    </Button>
                  </div>
                </div>
              )}

              {/* Basic Command */}
              <div className="p-3 bg-muted rounded-lg">
                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <p className="text-sm font-medium">Basic CLI Command:</p>
                    <code className="text-xs bg-background px-2 py-1 rounded block">
                      {generateBasicCommand(`${window.location.origin}/api/rules/raw?id=${rule.id}`)}
                    </code>
                  </div>
                  <Button
                    size="1"
                    variant="outline"
                    onClick={handleCopyCommand}
                    className="ml-2"
                  >
                    <Terminal className="h-4 w-4 mr-2" />
                    {copiedCommand ? "Copied!" : "Copy"}
                  </Button>
                </div>
              </div>

              {/* Other Preferred IDEs */}
              {idePreferences.preferredIDEs.length > 0 && (
                <div className="space-y-2">
                  <p className="text-sm font-medium">Other IDE Commands:</p>
                  <div className="grid gap-2">
                    {sortIDEPreferences(idePreferences.preferredIDEs, idePreferences.defaultIDE)
                      .filter(ide => ide.id !== idePreferences.defaultIDE)
                      .map((ide) => (
                        <div key={ide.id} className="flex items-center justify-between p-2 bg-muted/50 rounded text-xs">
                          <div className="flex items-center gap-2 min-w-0 flex-1">
                            <Code className="h-3 w-3 flex-shrink-0" />
                            <span className="font-medium flex-shrink-0">{ide.name}:</span>
                            <code className="bg-background px-1 py-0.5 rounded truncate">
                              {generateIDECommand(`${window.location.origin}/api/rules/raw?id=${rule.id}`, ide.type)}
                            </code>
                          </div>
                          <Button
                            size="1"
                            variant="ghost"
                            onClick={() => handleCopyIDECommand(ide.id)}
                            className="ml-2 h-6 w-6 p-0"
                          >
                            <Copy className="h-3 w-3" />
                          </Button>
                        </div>
                      ))}
                  </div>
                </div>
              )}
            </div>
          )}
        </Dialog.Content>
      </Dialog.Root>
    </>
  );
}